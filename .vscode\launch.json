{"version": "0.2.0", "configurations": [{"name": "🚀 Run USB PD Parser", "type": "debugpy", "request": "launch", "program": "${workspaceFolder}/usb_pd_parser.py", "args": ["sample_usb_pd.pdf", "-v"], "console": "integratedTerminal", "cwd": "${workspaceFolder}", "env": {}, "python": "${workspaceFolder}/venv/bin/python"}, {"name": "🔍 Debug USB PD Parser", "type": "debugpy", "request": "launch", "program": "${workspaceFolder}/usb_pd_parser.py", "args": ["sample_usb_pd.pdf", "-v", "-o", "debug_output.jsonl"], "console": "integratedTerminal", "cwd": "${workspaceFolder}", "stopOnEntry": false, "python": "${workspaceFolder}/venv/bin/python"}, {"name": "⚡ Quick Test (No Args)", "type": "debugpy", "request": "launch", "program": "${workspaceFolder}/usb_pd_parser.py", "args": ["--help"], "console": "integratedTerminal", "cwd": "${workspaceFolder}", "python": "${workspaceFolder}/venv/bin/python"}, {"name": "📝 Run with Custom PDF", "type": "debugpy", "request": "launch", "program": "${workspaceFolder}/usb_pd_parser.py", "args": ["${input:pdfFileName}", "-o", "${input:outputFileName}", "-v"], "console": "integratedTerminal", "cwd": "${workspaceFolder}", "python": "${workspaceFolder}/venv/bin/python"}], "inputs": [{"id": "pdfFileName", "description": "Enter PDF file name", "default": "usb_pd_spec.pdf", "type": "promptString"}, {"id": "outputFileName", "description": "Enter output JSONL file name", "default": "usb_pd_spec.jsonl", "type": "promptString"}]}