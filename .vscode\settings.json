{
    // Python interpreter settings
    "python.defaultInterpreterPath": "./venv/bin/python",
    "python.terminal.activateEnvironment": true,
    // Linting (code quality checks)
    "python.linting.enabled": true,
    "python.linting.pylintEnabled": true,
    "python.linting.flake8Enabled": true,
    "python.linting.lintOnSave": true,
    // Code formatting
    "python.formatting.provider": "black",
    "python.formatting.blackArgs": [
        "--line-length=100"
    ],
    "editor.formatOnSave": true,
    // File associations
    "files.associations": {
        "*.jsonl": "jsonl"
    },
    // Editor settings for Python
    "editor.tabSize": 4,
    "editor.insertSpaces": true,
    "editor.rulers": [
        88,
        100
    ],
    // Auto-save
    "files.autoSave": "afterDelay",
    "files.autoSaveDelay": 1000,
    // Terminal settings
    "terminal.integrated.cwd": "${workspaceFolder}",
    // Exclude files from explorer
    "files.exclude": {
        "**/__pycache__": true,
        "**/*.pyc": true,
        "venv/": false,
        ".pytest_cache": true
    },
    // IntelliSense settings
    "python.analysis.typeCheckingMode": "basic",
    "python.analysis.autoImportCompletions": true
}