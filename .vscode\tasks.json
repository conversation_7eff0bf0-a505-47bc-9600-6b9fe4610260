{"version": "2.0.0", "tasks": [{"label": "Install Dependencies", "type": "shell", "command": "pip", "args": ["install", "-r", "requirements.txt"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "Create Virtual Environment", "type": "shell", "command": "python", "args": ["-m", "venv", "venv"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}}, {"label": "<PERSON>", "type": "shell", "command": "python", "args": ["-m", "flake8", "usb_pd_parser.py"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}}, {"label": "Clean Output Files", "type": "shell", "command": "rm", "args": ["-f", "*.j<PERSON>l"], "group": "build", "windows": {"command": "del", "args": ["*.j<PERSON>l"]}}]}