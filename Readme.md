# USB PD Specification Parser

A Python tool to extract and structure Table of Contents (ToC) from USB Power Delivery specification PDFs into machine-readable JSONL format.

## 🚀 Quick Start

### Prerequisites
- Python 3.8 or higher
- VS Code (recommended)
- PDF file of USB PD specification

### 1. Setup in VS Code

#### Clone/Download the project:
```bash
# Create a new directory for the project
mkdir usb_pd_parser
cd usb_pd_parser

# Copy the Python script and requirements.txt to this directory
```

#### Open in VS Code:
```bash
# Open VS Code in the project directory
code .
```

### 2. Install Dependencies

#### Option A: Using pip directly
```bash
pip install pdfplumber PyMuPDF pydantic
```

#### Option B: Using requirements.txt (Recommended)
```bash
pip install -r requirements.txt
```

#### Option C: Using virtual environment (Best Practice)
```bash
# Create virtual environment
python -m venv venv

# Activate virtual environment
# On Windows:
venv\Scripts\activate
# On macOS/Linux:
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt
```

### 3. Run the Parser

#### Basic Usage:
```bash
python usb_pd_parser.py your_usb_pd_spec.pdf
```

#### Advanced Usage:
```bash
# Specify custom output file
python usb_pd_parser.py usb_pd_spec.pdf -o custom_output.jsonl

# Enable verbose logging
python usb_pd_parser.py usb_pd_spec.pdf -v

# Both options together
python usb_pd_parser.py usb_pd_spec.pdf -o my_output.jsonl -v
```

## 🛠️ VS Code Setup for Development

### 1. Install VS Code Extensions (Recommended)
- **Python** (Microsoft)
- **Python Docstring Generator** (Nils Werner)
- **JSON** (Built-in, for viewing JSONL output)

### 2. VS Code Settings
Create `.vscode/settings.json` in your project:
```json
{
    "python.defaultInterpreterPath": "./venv/bin/python",
    "python.linting.enabled": true,
    "python.linting.pylintEnabled": true,
    "python.formatting.provider": "black",
    "python.linting.flake8Enabled": true
}
```

### 3. VS Code Launch Configuration
Create `.vscode/launch.json`:
```json
{
    "version": "0.2.0",
    "configurations": [
        {
            "name": "USB PD Parser",
            "type": "python",
            "request": "launch",
            "program": "${workspaceFolder}/usb_pd_parser.py",
            "args": ["sample_usb_pd.pdf", "-v"],
            "console": "integratedTerminal",
            "cwd": "${workspaceFolder}"
        }
    ]
}
```

## 📋 Command Reference

### Terminal Commands in VS Code

#### Open Terminal in VS Code:
- **Ctrl+`** (backtick) or **View → Terminal**

#### Run Commands:
```bash
# Navigate to project directory (if not already there)
cd path/to/usb_pd_parser

# Activate virtual environment (if using)
source venv/bin/activate  # macOS/Linux
# or
venv\Scripts\activate     # Windows

# Run the parser
python usb_pd_parser.py sample.pdf

# View help
python usb_pd_parser.py --help

# Check Python version
python --version

# List installed packages
pip list

# View output file
cat usb_pd_spec.jsonl

# Count lines in output
wc -l usb_pd_spec.jsonl
```

## 📄 Input/Output Examples

### Expected Input (PDF):
- USB PD Specification document with Table of Contents
- Typically has numbered sections like:
  ```
  2 Overview ................................. 53
  2.1 Introduction ........................... 53
  2.1.1 Power Delivery Source Operational Contracts ... 53
  ```

### Expected Output (JSONL):
```json
{"doc_title": "USB Power Delivery Specification", "section_id": "2", "title": "Overview", "page": 53, "level": 1, "parent_id": null, "full_path": "2 Overview", "tags": []}
{"doc_title": "USB Power Delivery Specification", "section_id": "2.1", "title": "Introduction", "page": 53, "level": 2, "parent_id": "2", "full_path": "2.1 Introduction", "tags": []}
```

## 🔧 Troubleshooting

### Common Issues and Solutions:

#### 1. **"No module named 'pdfplumber'"**
```bash
pip install pdfplumber PyMuPDF pydantic
```

#### 2. **"PDF file not found"**
- Check file path is correct
- Ensure PDF file is in the same directory or provide full path

#### 3. **"No Table of Contents section found"**
- PDF might have non-standard ToC format
- Try different pages with manual inspection
- Check if ToC uses different keywords

#### 4. **Permission errors**
```bash
# On Windows, run as administrator if needed
# On macOS/Linux:
sudo python usb_pd_parser.py your_file.pdf
```

#### 5. **Python version issues**
```bash
# Check Python version
python --version

# Use python3 if needed
python3 usb_pd_parser.py your_file.pdf
```

## 📊 Features

- ✅ **Multi-library support**: Uses both pdfplumber and PyMuPDF
- ✅ **Robust parsing**: Multiple regex patterns for different ToC formats
- ✅ **Hierarchical structure**: Automatically detects parent-child relationships
- ✅ **Auto-tagging**: Generates semantic tags based on section titles
- ✅ **Error handling**: Graceful fallbacks and detailed logging
- ✅ **JSON validation**: Uses Pydantic for data validation
- ✅ **Flexible output**: Customizable JSONL output path

## 🎯 Next Steps

1. **Test with your USB PD PDF**
2. **Validate the output JSONL**
3. **Extend for full document parsing** (beyond ToC)
4. **Integrate with downstream applications**

## 📝 Project Structure

```
usb_pd_parser/
├── usb_pd_parser.py       # Main parser script
├── requirements.txt       # Python dependencies
├── README.md             # This file
├── usb_pd_spec.jsonl     # Output file (generated)
├── venv/                 # Virtual environment (optional)
└── .vscode/              # VS Code configuration
    ├── settings.json
    └── launch.json
```

## 🔍 Understanding the Output

Each line in the JSONL file represents one section from the ToC:

- **doc_title**: Document name/title
- **section_id**: Hierarchical section number (e.g., "2.1.2")
- **title**: Section title text
- **page**: Starting page number
- **level**: Depth in hierarchy (1=chapter, 2=section, 3=subsection)
- **parent_id**: Parent section ID (null for top-level)
- **full_path**: Complete section identifier + title
- **tags**: Automatically generated semantic tags

Ready to parse your USB PD specifications! 🚀