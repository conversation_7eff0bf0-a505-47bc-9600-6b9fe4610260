#!/usr/bin/env python3
"""
USB Power Delivery (USB PD) Specification Parser
=================================================

This script extracts Table of Contents (ToC) from USB PD specification PDFs
and converts them into structured JSONL format for downstream processing.

Author: AI Assistant
Date: 2025
Version: 1.0

Requirements:
- pdfplumber: pip install pdfplumber
- PyMuPDF: pip install PyMuPDF
- pydantic: pip install pydantic

Usage:
    python usb_pd_parser.py <pdf_file_path>
    
Example:
    python usb_pd_parser.py usb_pd_spec.pdf
"""

import re
import json
import sys
import argparse
from typing import List, Dict, Optional, Any
from pathlib import Path
import logging

# Third-party imports
try:
    import pdfplumber
    import fitz  # PyMuPDF
    from pydantic import BaseModel, Field
except ImportError as e:
    print(f"Missing required library: {e}")
    print("Please install: pip install pdfplumber PyMuPDF pydantic")
    sys.exit(1)

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class ToCEntry(BaseModel):
    """Data model for Table of Contents entry"""
    doc_title: str
    section_id: str
    title: str
    page: int
    level: int
    parent_id: Optional[str] = None
    full_path: str
    tags: List[str] = Field(default_factory=list)


class USBPDParser:
    """Main parser class for USB PD specification documents"""
    
    def __init__(self, pdf_path: str):
        self.pdf_path = Path(pdf_path)
        self.doc_title = "USB Power Delivery Specification"
        self.toc_entries: List[ToCEntry] = []
        
        # Regex patterns for ToC parsing
        self.toc_patterns = [
            # Pattern 1: "2.1.2 Title .... 53"
            r'^(\d+(?:\.\d+)*)\s+([^\.\n]+?)[\.\s]*\s+(\d+)\s*$',
            # Pattern 2: "2.1.2 Title 53"  
            r'^(\d+(?:\.\d+)*)\s+(.+?)\s+(\d+)\s*$',
            # Pattern 3: More flexible pattern
            r'^(\d+(?:\.\d+)*)\s+(.+?)[\s\.]+(\d+)\s*$'
        ]
    
    def extract_text_pdfplumber(self, start_page: int = 0, end_page: int = 15) -> List[str]:
        """Extract text from PDF using pdfplumber"""
        pages_text = []
        try:
            with pdfplumber.open(self.pdf_path) as pdf:
                total_pages = len(pdf.pages)
                end_page = min(end_page, total_pages)
                
                logger.info(f"Extracting text from pages {start_page+1} to {end_page}")
                
                for i in range(start_page, end_page):
                    page = pdf.pages[i]
                    text = page.extract_text()
                    if text:
                        pages_text.append(text)
                        
        except Exception as e:
            logger.error(f"Error extracting text with pdfplumber: {e}")
            raise
            
        return pages_text
    
    def extract_text_pymupdf(self, start_page: int = 0, end_page: int = 15) -> List[str]:
        """Extract text from PDF using PyMuPDF as fallback"""
        pages_text = []
        try:
            doc = fitz.open(self.pdf_path)
            total_pages = len(doc)
            end_page = min(end_page, total_pages)
            
            logger.info(f"Extracting text with PyMuPDF from pages {start_page+1} to {end_page}")
            
            for i in range(start_page, end_page):
                page = doc.load_page(i)
                text = page.get_text("text")  # type: ignore
                if text:
                    pages_text.append(text)
                    
            doc.close()
            
        except Exception as e:
            logger.error(f"Error extracting text with PyMuPDF: {e}")
            raise
            
        return pages_text
    
    def find_toc_section(self, pages_text: List[str]) -> Optional[str]:
        """Find and extract the Table of Contents section"""
        toc_keywords = ['table of contents', 'contents', 'toc']
        toc_text = ""
        
        for page_text in pages_text:
            lines = page_text.split('\n')
            
            # Look for ToC header
            for i, line in enumerate(lines):
                line_lower = line.lower().strip()
                
                if any(keyword in line_lower for keyword in toc_keywords):
                    logger.info(f"Found ToC header: {line.strip()}")
                    
                    # Extract text after ToC header
                    remaining_lines = lines[i+1:]
                    for remaining_line in remaining_lines:
                        if remaining_line.strip():
                            toc_text += remaining_line + '\n'
                    
                    # If we found substantial ToC content, return it
                    if len(toc_text) > 200:  # Reasonable ToC size
                        return toc_text
        
        # If no explicit ToC header found, look for numbered sections pattern
        logger.warning("No explicit ToC header found, searching for numbered sections...")
        for page_text in pages_text[:5]:  # Check first 5 pages
            if self.has_toc_pattern(page_text):
                return page_text
        
        return None
    
    def has_toc_pattern(self, text: str) -> bool:
        """Check if text contains ToC-like patterns"""
        lines = text.split('\n')
        pattern_count = 0
        
        for line in lines:
            line = line.strip()
            if re.match(r'^\d+(?:\.\d+)*\s+.+\s+\d+\s*$', line):
                pattern_count += 1
                if pattern_count >= 3:  # At least 3 ToC-like lines
                    return True
        
        return False
    
    def parse_toc_line(self, line: str) -> Optional[Dict[str, Any]]:
        """Parse a single ToC line using regex patterns"""
        line = line.strip()
        if not line or len(line) < 5:
            return None
        
        for pattern in self.toc_patterns:
            match = re.match(pattern, line)
            if match:
                try:
                    section_id = match.group(1).strip()
                    title = match.group(2).strip()
                    page = int(match.group(3).strip())
                    
                    # Clean up title (remove excessive dots)
                    title = re.sub(r'\.{3,}', '', title).strip()
                    
                    # Calculate level based on dots in section_id
                    level = section_id.count('.') + 1
                    
                    # Calculate parent_id
                    parent_id = None
                    if '.' in section_id:
                        parent_parts = section_id.split('.')
                        parent_id = '.'.join(parent_parts[:-1])
                    
                    # Generate full path
                    full_path = f"{section_id} {title}"
                    
                    return {
                        'section_id': section_id,
                        'title': title,
                        'page': page,
                        'level': level,
                        'parent_id': parent_id,
                        'full_path': full_path
                    }
                    
                except (IndexError, ValueError) as e:
                    logger.debug(f"Failed to parse line '{line}': {e}")
                    continue
        
        return None
    
    def extract_toc_entries(self, toc_text: str) -> List[ToCEntry]:
        """Extract all ToC entries from the ToC text"""
        entries = []
        lines = toc_text.split('\n')
        
        logger.info(f"Processing {len(lines)} lines for ToC entries")
        
        for line_num, line in enumerate(lines, 1):
            parsed = self.parse_toc_line(line)
            if parsed:
                try:
                    entry = ToCEntry(
                        doc_title=self.doc_title,
                        section_id=parsed['section_id'],
                        title=parsed['title'],
                        page=parsed['page'],
                        level=parsed['level'],
                        parent_id=parsed['parent_id'],
                        full_path=parsed['full_path'],
                        tags=self.generate_tags(parsed['title'])
                    )
                    entries.append(entry)
                    logger.debug(f"Line {line_num}: {parsed['section_id']} - {parsed['title']}")
                    
                except Exception as e:
                    logger.error(f"Error creating ToCEntry for line {line_num}: {e}")
                    continue
        
        logger.info(f"Successfully parsed {len(entries)} ToC entries")
        return entries
    
    def generate_tags(self, title: str) -> List[str]:
        """Generate semantic tags based on section title"""
        tags = []
        title_lower = title.lower()
        
        # Define tag mappings
        tag_keywords = {
            'negotiation': ['negotiation', 'negotiate'],
            'contracts': ['contract', 'agreement'],
            'source': ['source'],
            'sink': ['sink'],
            'communication': ['communication', 'message', 'protocol'],
            'cable': ['cable', 'plug'],
            'power': ['power', 'voltage', 'current'],
            'device': ['device', 'equipment'],
            'revision': ['revision', 'version'],
            'applications': ['application', 'use', 'usage'],
            'collision': ['collision'],
            'avoidance': ['avoidance', 'avoid'],
            'testing': ['test', 'verification', 'validation']
        }
        
        for tag, keywords in tag_keywords.items():
            if any(keyword in title_lower for keyword in keywords):
                tags.append(tag)
        
        return tags
    
    def save_jsonl(self, output_path: Optional[str] = None) -> str:
        """Save ToC entries to JSONL file"""
        if output_path is None:
            output_path = "usb_pd_spec.jsonl"
        
        output_file = Path(output_path)
        
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                for entry in self.toc_entries:
                    json_line = entry.model_dump_json()
                    f.write(json_line + '\n')
            
            logger.info(f"Successfully saved {len(self.toc_entries)} entries to {output_file}")
            return str(output_file)
            
        except Exception as e:
            logger.error(f"Error saving JSONL file: {e}")
            raise
    
    def generate_summary_report(self) -> Dict[str, Any]:
        """Generate a summary report of parsed ToC"""
        if not self.toc_entries:
            return {"error": "No ToC entries found"}
        
        # Count entries by level
        level_counts = {}
        for entry in self.toc_entries:
            level_counts[entry.level] = level_counts.get(entry.level, 0) + 1
        
        # Find page range
        pages = [entry.page for entry in self.toc_entries]
        
        report = {
            "doc_title": self.doc_title,
            "total_entries": len(self.toc_entries),
            "level_breakdown": level_counts,
            "page_range": {
                "min": min(pages) if pages else 0,
                "max": max(pages) if pages else 0
            },
            "sample_entries": [
                {
                    "section_id": entry.section_id,
                    "title": entry.title,
                    "page": entry.page,
                    "level": entry.level
                }
                for entry in self.toc_entries[:5]  # First 5 entries
            ]
        }
        
        return report
    
    def parse(self) -> bool:
        """Main parsing method"""
        logger.info(f"Starting to parse PDF: {self.pdf_path}")
        
        if not self.pdf_path.exists():
            logger.error(f"PDF file not found: {self.pdf_path}")
            return False
        
        try:
            # Try pdfplumber first, fall back to PyMuPDF
            pages_text = []
            try:
                pages_text = self.extract_text_pdfplumber()
            except Exception as e:
                logger.warning(f"pdfplumber failed: {e}. Trying PyMuPDF...")
                pages_text = self.extract_text_pymupdf()
            
            if not pages_text:
                logger.error("No text extracted from PDF")
                return False
            
            # Find ToC section
            toc_text = self.find_toc_section(pages_text)
            if not toc_text:
                logger.error("No Table of Contents section found")
                return False
            
            # Extract ToC entries
            self.toc_entries = self.extract_toc_entries(toc_text)
            
            if not self.toc_entries:
                logger.error("No valid ToC entries found")
                return False
            
            logger.info("Parsing completed successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error during parsing: {e}")
            return False


def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description="Parse USB PD Specification PDF and extract ToC")
    parser.add_argument("pdf_file", help="Path to the USB PD specification PDF file")
    parser.add_argument("-o", "--output", default="usb_pd_spec.jsonl", 
                       help="Output JSONL file path (default: usb_pd_spec.jsonl)")
    parser.add_argument("-v", "--verbose", action="store_true", 
                       help="Enable verbose logging")
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Initialize parser
    usb_parser = USBPDParser(args.pdf_file)
    
    # Parse the PDF
    if not usb_parser.parse():
        print("❌ Parsing failed. Check logs for details.")
        sys.exit(1)
    
    # Save to JSONL
    try:
        output_file = usb_parser.save_jsonl(args.output)
        print(f"✅ Successfully saved ToC entries to: {output_file}")
    except Exception as e:
        print(f"❌ Error saving output: {e}")
        sys.exit(1)
    
    # Generate and display summary report
    report = usb_parser.generate_summary_report()
    print("\n📊 PARSING SUMMARY")
    print("=" * 50)
    print(f"Document: {report['doc_title']}")
    print(f"Total entries: {report['total_entries']}")
    print(f"Page range: {report['page_range']['min']} - {report['page_range']['max']}")
    print(f"Level breakdown: {report['level_breakdown']}")
    
    print("\n📄 SAMPLE ENTRIES:")
    for i, entry in enumerate(report['sample_entries'], 1):
        print(f"{i}. {entry['section_id']} - {entry['title']} (p. {entry['page']}, level {entry['level']})")
    
    print(f"\n🎉 Processing complete! Check '{output_file}' for full results.")


if __name__ == "__main__":
    main()